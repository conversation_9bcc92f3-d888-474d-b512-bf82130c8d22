import { fetcher } from "@/lib/fetcher";
import { useQuery, useMutation, QueryClient } from '@tanstack/react-query';


export interface IPhonepassword {
  mobileNumber: string;
  password: string;
  clientId: string;
  code: number;
  countryCode: string | number;
  signinType: string;
  extra: {
    [key: string]: unknown;
  };
}

interface IPhoneCode {
  mobileNumber: string;
  countryCode: string | number;
  clientId: string;
  captcha?: {
    captchaID: string;
    captchaOutput: string;
    genTime: string;
    lotNumber: string;
    passToken: string;
  };
  category: string;
  codeLength: number;
  language: string;
}

interface IEmailCode {
  email: string;
  countryCode: string | number;
  clientId: string;
  captcha: {
    captchaID: string;
    captchaOutput: string;
    genTime: string;
    lotNumber: string;
    passToken: string;
  };
  category: string;
  codeLength: number;
  language: string;
}

interface IEmailPassword {
  email: string;
  password: string;
  clientId: string;
  code: number;
  signinType: string;
  extra: { [key: string]: unknown };
}

interface LoginResponse {
  accessToken: string;
  refreshToken?: string;
  expiresIn?: number;
}

export const phonePasswordLogin = async (
  data: IPhonepassword
): Promise<LoginResponse> => {
  const response = await fetcher<LoginResponse>("/v1/phone/signin", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });
  

  return response;
};

export const phoneCodeSend = async (
  data: IPhoneCode
): Promise<LoginResponse> => {
  const response = await fetcher<LoginResponse>("/v1/phone/code/send", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });

  return response;
};

export const emailCodeLogin = async (
  data: IEmailCode
): Promise<LoginResponse> => {
  const response = await fetcher<LoginResponse>("/v1/email/code/send", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });

  return response;
};

export const emailPasswordLogin = async (
  data: IEmailPassword
): Promise<LoginResponse> => {
  const response = await fetcher<LoginResponse>("/v1/email/signin", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });

  return response;
};
