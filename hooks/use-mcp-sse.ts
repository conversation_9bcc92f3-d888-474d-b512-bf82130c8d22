"use client"

import { useEffect, useRef, useState, useCallback } from 'react'

export interface MCPMessage {
  type: string
  data: any
  timestamp: string
}

export interface MCPSSEOptions {
  clientId?: string
  autoReconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
}

export interface MCPSSEHook {
  isConnected: boolean
  isConnecting: boolean
  error: string | null
  messages: MCPMessage[]
  lastMessage: MCPMessage | null
  sendMessage: (message: any, type?: string) => Promise<boolean>
  sendMCPRequest: (method: string, params?: any) => Promise<any>
  connect: () => void
  disconnect: () => void
  clearMessages: () => void
  connectionId: string | null
}

export function useMCPSSE(options: MCPSSEOptions = {}): MCPSSEHook {
  const {
    clientId = `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    autoReconnect = true,
    reconnectInterval = 3000,
    maxReconnectAttempts = 5
  } = options

  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [messages, setMessages] = useState<MCPMessage[]>([])
  const [lastMessage, setLastMessage] = useState<MCPMessage | null>(null)
  const [connectionId, setConnectionId] = useState<string | null>(null)

  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const connect = useCallback(() => {
    if (eventSourceRef.current || isConnecting) {
      return
    }

    setIsConnecting(true)
    setError(null)

    try {
      const url = `/api/mcp/sse?clientId=${encodeURIComponent(clientId)}`
      const eventSource = new EventSource(url)

      eventSource.onopen = () => {
        setIsConnected(true)
        setIsConnecting(false)
        setError(null)
        setConnectionId(clientId)
        reconnectAttemptsRef.current = 0
        console.log('MCP SSE connected:', clientId)
      }

      eventSource.onmessage = (event) => {
        try {
          const message: MCPMessage = JSON.parse(event.data)
          setMessages(prev => [...prev, message])
          setLastMessage(message)
          
          // 处理特殊消息类型
          if (message.type === 'connection') {
            console.log('MCP connection established:', message.data)
          }
        } catch (err) {
          console.error('Failed to parse SSE message:', err)
        }
      }

      eventSource.onerror = (event) => {
        console.error('MCP SSE error:', event)
        setIsConnected(false)
        setIsConnecting(false)
        setError('Connection error')
        
        eventSource.close()
        eventSourceRef.current = null

        // 自动重连
        if (autoReconnect && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++
          console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectInterval)
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          setError(`Failed to reconnect after ${maxReconnectAttempts} attempts`)
        }
      }

      eventSourceRef.current = eventSource

    } catch (err) {
      setIsConnecting(false)
      setError(err instanceof Error ? err.message : 'Failed to connect')
    }
  }, [clientId, autoReconnect, reconnectInterval, maxReconnectAttempts, isConnecting])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }

    setIsConnected(false)
    setIsConnecting(false)
    setConnectionId(null)
    reconnectAttemptsRef.current = 0
  }, [])

  const sendMessage = useCallback(async (message: any, type: string = 'message'): Promise<boolean> => {
    if (!isConnected || !connectionId) {
      throw new Error('Not connected to MCP SSE')
    }

    try {
      const response = await fetch('/api/mcp/sse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId: connectionId,
          message,
          type
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`)
      }

      return true
    } catch (err) {
      console.error('Failed to send message:', err)
      return false
    }
  }, [isConnected, connectionId])

  const sendMCPRequest = useCallback(async (method: string, params?: any): Promise<any> => {
    if (!isConnected || !connectionId) {
      throw new Error('Not connected to MCP SSE')
    }

    const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    try {
      const response = await fetch('/api/mcp/server', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: requestId,
          method,
          params: {
            ...params,
            clientId: connectionId
          }
        })
      })

      if (!response.ok) {
        throw new Error(`MCP request failed: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.error) {
        throw new Error(`MCP error: ${result.error.message}`)
      }

      return result.result
    } catch (err) {
      console.error('MCP request failed:', err)
      throw err
    }
  }, [isConnected, connectionId])

  const clearMessages = useCallback(() => {
    setMessages([])
    setLastMessage(null)
  }, [])

  // 自动连接
  useEffect(() => {
    connect()
    
    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  // 清理
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    isConnected,
    isConnecting,
    error,
    messages,
    lastMessage,
    sendMessage,
    sendMCPRequest,
    connect,
    disconnect,
    clearMessages,
    connectionId
  }
}
