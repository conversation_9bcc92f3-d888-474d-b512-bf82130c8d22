import { QueryClient, useMutation } from '@tanstack/react-query';
import { phonePasswordLogin } from '@/server/login';


export const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 2, // 失败重试2次
        staleTime: 1000 * 60 * 5, // 数据缓存5分钟
      },
      mutations: {
        onError: (error) => {
          console.error('Mutation error:', error.message);
          // 可添加 Toast 通知或日志上报
        },
      },
    },
  });
export const usePhonePasswordLogin = () => {
    return useMutation({
      mutationFn: phonePasswordLogin,
      onSuccess: (data) => {
        console.log('手机号密码登录成功:', data);
        // 可以在这里处理成功后的逻辑，比如保存 token
      },
      onError: (error) => {
        console.error('手机号密码登录失败:', error);
        // 可以在这里处理错误，比如显示错误提示
      },
    });
  };