import { QueryClient, useMutation } from "@tanstack/react-query";
import { phoneCodeSend, phonePasswordLogin } from "@/server/login";
import {
  checkoutOrder,
  checkoutMerchandises,
  checkoutCardsInfo,
  checkoutOrderStatus,
  checkoutContractList,
  checkoutContractCancel,
} from "@/server/checkout";

// 全局错误处理函数
const handleGlobalError = (error: Error) => {
  console.error("Query error:", error.message);

  // 检查是否是认证相关错误
  if (
    error.message === "AUTHENTICATION_REQUIRED" ||
    error.message === "AUTHENTICATION_FAILED" ||
    error.message.includes("401") ||
    error.message.includes("Unauthorized")
  ) {
    // 清除本地存储的token
    if (typeof window !== "undefined") {
      localStorage.removeItem("token");
      localStorage.removeItem("refreshToken");

      // 跳转到登录页面
      window.location.href = "/login";
    }
  }
};

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // 认证错误不重试
        if (
          error.message === "AUTHENTICATION_REQUIRED" ||
          error.message === "AUTHENTICATION_FAILED"
        ) {
          return false;
        }
        return failureCount < 2;
      },
      staleTime: 1000 * 60 * 5, // 数据缓存5分钟
      onError: handleGlobalError,
    },
    mutations: {
      onError: handleGlobalError,
    },
  },
});
export const usePhonePasswordLogin = () => {
  return useMutation({
    mutationFn: phonePasswordLogin,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const usePhoneCodeSend = () => {
  return useMutation({
    mutationFn: phoneCodeSend,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};


export const useCheckoutOrder = () => {
  return useMutation({
    mutationFn: checkoutOrder,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};

export const useCheckoutMerchandises = () => {
  return useMutation({
    mutationFn: checkoutMerchandises,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutCardsInfo = () => {
  return useMutation({
    mutationFn: checkoutCardsInfo,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutOrderStatus = () => {
  return useMutation({
    mutationFn: checkoutOrderStatus,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutContractList = () => {
  return useMutation({
    mutationFn: checkoutContractList,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutContractCancel = () => {
  return useMutation({
    mutationFn: checkoutContractCancel,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};


