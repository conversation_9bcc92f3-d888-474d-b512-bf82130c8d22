import { isStage } from "./utils";
import { getHmacVersion2 } from "./hmacv2";

interface FetcherOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE";
  body?: any;
  headers?: Record<string, string>;
  needAuth?: boolean;
}
// 获取 token 的函数（根据你的认证方式调整）
const getToken = (): string | null => {
  // 示例：从 localStorage 或 cookies 获取 token
  if (typeof window !== "undefined") {
    return localStorage.getItem("token");
  }
  return null; // 服务器端默认无 token
};

export async function fetcher<T>(
  url: string,
  options: FetcherOptions & { needAuth?: boolean } = {}
) {
  const token = getToken();
  const { needAuth } = options;

  if (isStage()) {
    const separator = url.includes("?") ? "&" : "?";
    url += `${separator}user_id=131362`;
  }

  // 检查是否需要认证且缺少 token
  if (needAuth && !token && typeof window !== "undefined") {
    // 抛出特定的认证错误，让上层处理跳转
    throw new Error("AUTHENTICATION_REQUIRED");
  }

  const hmac = await getHmacVersion2(url, options.body);

  const headers = {
    "Content-Type": "application/json",
    clientOS: "web",
    clientId: "100033",
    "X-Putong-App-Info": `web/putong/1.0.0`,
    Authorization: hmac,
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...options.headers,
  };
  const res = await fetch(url, { ...options, headers });

  if (!res.ok) {
    const errorText = await res.text();

    // 检查是否是认证相关错误
    if (res.status === 400 || res.status === 401 || res.status === 403) {
      throw new Error("AUTHENTICATION_FAILED");
    }

    throw new Error(errorText);
  }

  return res.json() as Promise<T>;
}
