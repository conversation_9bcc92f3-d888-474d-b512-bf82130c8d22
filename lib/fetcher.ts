import { redirect, useRouter } from "next/navigation";

interface FetcherOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE";
  body?: any;
  headers?: Record<string, string>;
  needAuth?: boolean;
}
// 获取 token 的函数（根据你的认证方式调整）
const getToken = (): string | null => {
  // 示例：从 localStorage 或 cookies 获取 token
  if (typeof window !== "undefined") {
    return localStorage.getItem("token");
  }
  return null; // 服务器端默认无 token
};

export async function fetcher<T>(
  url: string,
  options: FetcherOptions & { needAuth?: boolean } = {}
) {
  const token = getToken();
  const { needAuth } = options;

  // 检查是否需要认证且缺少 token
  if (needAuth && !token && typeof window !== "undefined") {
    // 客户端环境：跳转到登录页面
    const router = useRouter();
    router.push("/login");
    throw new Error("No token found, redirecting to login");
  }

  const headers = {
    "Content-Type": "application/json",
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...options.headers,
  };
  const res = await fetch(url, { ...options, headers });
  if (!res.ok) {
    throw new Error(await res.text());
  }
  return res.json() as Promise<T>;
}
