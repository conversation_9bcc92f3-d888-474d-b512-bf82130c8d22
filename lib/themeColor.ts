// 主题颜色配置文件
export const themeColors = {
  // VIP 主题颜色
  vip: {
    primary: "#FFDC7A",
    secondary: "#FDE68A",
    border: "#FBBF24",
    background: "#FEFDF7",
    card: "#FFDC7A",
    text: "#1F2937",
    textSecondary: "rgba(0,0,0,0.4)",
    tabBgColor: "#FEFDF7",
    priceMonth: "#DFB43A",
  },

  // SEE 主题颜色
  see: {
    primary: "#FF8817",
    secondary: "#FDBA74",
    border: "#FEFDF7",
    background: "#FEFDF7",
    card: "#FFFFFF",
    text: "#1F2937",
    textSecondary: "#6B7280",
    tabBgColor: "#FEFDF7",
    priceMonth: "#FE7E1D",
  },

  // PREMIUM 主题颜色
  premium: {
    primary: "#fddfa3",
    secondary: "#EAB308",
    border: "#6d614b",
    background: "rgb(40 37 30)",
    card: "#1F2937",
    text: "#FFFFFF",
    textSecondary: "#D1D5DB",
    tabBgColor: "#433e34",
    priceMonth: "#DFB43A",
  },

  // ULTRA PREMIUM 主题颜色
  ultra: {
    primary: "#F9E7FF",
    secondary: "#A855F7",
    border: "rgb(83 74 59)",
    background: "#120F10",
    card: "#F9E7FF",
    text: "#FFFFFF",
    textSecondary: "#D1D5DB",
    tabBgColor: "rgb(32 29 30)",
    priceMonth: "#F3CEFF",
  },
} as const;

// 生成 Tailwind 颜色配置
export const generateTailwindColors = () => {
  const colors: Record<string, string> = {};

  Object.entries(themeColors).forEach(([themeName, themeConfig]) => {
    Object.entries(themeConfig).forEach(([colorName, colorValue]) => {
      colors[`${themeName}-${colorName}`] = colorValue;
    });
  });
  console.log(colors);
  return colors;
};

// 生成 safelist 数组
export const generateSafelist = () => {
  const safelist: string[] = [];

  Object.entries(themeColors).forEach(([themeName, themeConfig]) => {
    Object.entries(themeConfig).forEach(([colorName, colorValue]) => {
      // 背景色
      safelist.push(`bg-${themeName}-${colorName}`);
      safelist.push(`bg-[${colorValue}]`);

      // 文字颜色
      safelist.push(`text-${themeName}-${colorName}`);
      safelist.push(`text-[${colorValue}]`);

      // 边框颜色
      safelist.push(`border-${themeName}-${colorName}`);
      safelist.push(`border-[${colorValue}]`);

      // 悬停状态
      safelist.push(`hover:bg-${themeName}-${colorName}`);
      safelist.push(`hover:text-${themeName}-${colorName}`);
      safelist.push(`hover:border-${themeName}-${colorName}`);
    });
  });

  console.log(safelist);

  return safelist;
};

// 类型定义
export type ThemeName = keyof typeof themeColors;
export type ColorName = keyof typeof themeColors.vip;
