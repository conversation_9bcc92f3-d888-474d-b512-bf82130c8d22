export interface CountryCode {
    code: string
    country: string
    flag: string
    dialCode: string
  }
  
  export const countryCodes: CountryCode[] = [
    {
      code: "SG",
      country: "Singapore",
      flag: "🇸🇬",
      dialCode: "+65",
    },
    {
      code: "CN",
      country: "China",
      flag: "🇨🇳",
      dialCode: "+86",
    },
    {
      code: "US",
      country: "United States (USA)",
      flag: "🇺🇸",
      dialCode: "+1",
    },
    {
      code: "GB",
      country: "United Kingdom",
      flag: "🇬🇧",
      dialCode: "+44",
    },
    {
      code: "AU",
      country: "Australia",
      flag: "🇦🇺",
      dialCode: "+61",
    },
    {
      code: "JP",
      country: "Japan",
      flag: "🇯🇵",
      dialCode: "+81",
    },
    {
      code: "KR",
      country: "South Korea",
      flag: "🇰🇷",
      dialCode: "+82",
    },
    {
      code: "IN",
      country: "India",
      flag: "🇮🇳",
      dialCode: "+91",
    },
    {
      code: "DE",
      country: "Germany",
      flag: "🇩🇪",
      dialCode: "+49",
    },
    {
      code: "FR",
      country: "France",
      flag: "🇫🇷",
      dialCode: "+33",
    },
  ]
  
  export const getCountryByDialCode = (dialCode: string): CountryCode | undefined => {
    return countryCodes.find((country) => country.dialCode === dialCode)
  }
  
  export const getDefaultCountry = (): CountryCode => {
    return countryCodes.find((country) => country.code === "CN") || countryCodes[0]
  }
  