"use client"

import React from 'react'
import { useMCPSSE } from '@/hooks/use-mcp-sse'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  MessageCircle, 
  Trash2,
  RefreshCw,
  AlertCircle
} from 'lucide-react'

interface MCPStatusProps {
  className?: string
  showMessages?: boolean
  maxMessages?: number
}

export function MCPStatus({ 
  className = "", 
  showMessages = true, 
  maxMessages = 10 
}: MCPStatusProps) {
  const {
    isConnected,
    isConnecting,
    error,
    messages,
    lastMessage,
    sendMessage,
    sendMCPRequest,
    connect,
    disconnect,
    clearMessages,
    connectionId
  } = useMCPSSE({
    autoReconnect: true,
    maxReconnectAttempts: 3
  })

  const handleTestMessage = async () => {
    try {
      await sendMessage({ test: 'Hello from client', timestamp: new Date().toISOString() })
    } catch (err) {
      console.error('Failed to send test message:', err)
    }
  }

  const handlePing = async () => {
    try {
      const result = await sendMCPRequest('ping')
      console.log('Ping result:', result)
    } catch (err) {
      console.error('Ping failed:', err)
    }
  }

  const handleInitialize = async () => {
    try {
      const result = await sendMCPRequest('initialize', {
        clientInfo: {
          name: 'checkout-payment-client',
          version: '1.0.0'
        }
      })
      console.log('Initialize result:', result)
    } catch (err) {
      console.error('Initialize failed:', err)
    }
  }

  const getStatusIcon = () => {
    if (isConnecting) {
      return <Loader2 className="h-4 w-4 animate-spin" />
    }
    if (isConnected) {
      return <Wifi className="h-4 w-4" />
    }
    return <WifiOff className="h-4 w-4" />
  }

  const getStatusBadge = () => {
    if (isConnecting) {
      return <Badge variant="secondary">连接中...</Badge>
    }
    if (isConnected) {
      return <Badge variant="default">已连接</Badge>
    }
    if (error) {
      return <Badge variant="destructive">错误</Badge>
    }
    return <Badge variant="outline">未连接</Badge>
  }

  const displayMessages = messages.slice(-maxMessages)

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span>MCP SSE 状态</span>
          </div>
          {getStatusBadge()}
        </CardTitle>
        
        {connectionId && (
          <p className="text-sm text-muted-foreground">
            连接ID: {connectionId}
          </p>
        )}
        
        {error && (
          <div className="flex items-center gap-2 text-sm text-destructive">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 控制按钮 */}
        <div className="flex flex-wrap gap-2">
          {!isConnected && !isConnecting && (
            <Button size="sm" onClick={connect}>
              <Wifi className="h-4 w-4 mr-1" />
              连接
            </Button>
          )}
          
          {isConnected && (
            <>
              <Button size="sm" variant="outline" onClick={disconnect}>
                <WifiOff className="h-4 w-4 mr-1" />
                断开
              </Button>
              
              <Button size="sm" variant="outline" onClick={handleTestMessage}>
                <MessageCircle className="h-4 w-4 mr-1" />
                测试消息
              </Button>
              
              <Button size="sm" variant="outline" onClick={handlePing}>
                <RefreshCw className="h-4 w-4 mr-1" />
                Ping
              </Button>
              
              <Button size="sm" variant="outline" onClick={handleInitialize}>
                初始化
              </Button>
            </>
          )}
          
          {messages.length > 0 && (
            <Button size="sm" variant="outline" onClick={clearMessages}>
              <Trash2 className="h-4 w-4 mr-1" />
              清空消息
            </Button>
          )}
        </div>

        {/* 最新消息 */}
        {lastMessage && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">最新消息:</h4>
            <div className="p-2 bg-muted rounded-md">
              <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                <span>类型: {lastMessage.type}</span>
                <span>{new Date(lastMessage.timestamp).toLocaleTimeString()}</span>
              </div>
              <pre className="text-xs overflow-x-auto">
                {JSON.stringify(lastMessage.data, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* 消息历史 */}
        {showMessages && displayMessages.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">消息历史:</h4>
              <span className="text-xs text-muted-foreground">
                {displayMessages.length} / {messages.length}
              </span>
            </div>
            
            <ScrollArea className="h-48 w-full border rounded-md p-2">
              <div className="space-y-2">
                {displayMessages.map((message, index) => (
                  <div key={index} className="text-xs">
                    <div className="flex items-center justify-between text-muted-foreground mb-1">
                      <Badge variant="outline" className="text-xs">
                        {message.type}
                      </Badge>
                      <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                    </div>
                    <pre className="bg-muted p-1 rounded text-xs overflow-x-auto">
                      {JSON.stringify(message.data, null, 2)}
                    </pre>
                    {index < displayMessages.length - 1 && <Separator className="my-2" />}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* 连接统计 */}
        <div className="text-xs text-muted-foreground space-y-1">
          <div>总消息数: {messages.length}</div>
          <div>连接状态: {isConnected ? '已连接' : '未连接'}</div>
          {isConnected && connectionId && (
            <div>会话ID: {connectionId.slice(-8)}</div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
