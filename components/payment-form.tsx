"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useMCPSSE } from "@/hooks/use-mcp-sse"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Loader2,
  CreditCard,
  Shield,
  Lock,
  CheckCircle2,
  AlertCircle,
  Eye,
  EyeOff
} from "lucide-react"
import type { PaymentRequest, FramesCardTokenizedEvent } from "@/types/payment"

declare global {
  interface Window {
    Frames: any
  }
}

interface PaymentFormProps {
  onPaymentSuccess: (paymentId: string) => void
  onPaymentError: (error: string) => void
}

export function PaymentForm({ onPaymentSuccess, onPaymentError }: PaymentFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isFramesReady, setIsFramesReady] = useState(false)
  const [customerInfo, setCustomerInfo] = useState({
    email: "",
    name: "",
    amount: 1000, // 默认 $10.00 (以分为单位)
  })
  const [error, setError] = useState<string | null>(null)
  const [cardValidation, setCardValidation] = useState({
    cardNumber: false,
    expiryDate: false,
    cvv: false,
  })
  const [detectedCardType, setDetectedCardType] = useState<string | null>(null)
  const [showSecurityInfo, setShowSecurityInfo] = useState(false)
  const framesInitialized = useRef(false)

  // MCP SSE 连接
  const {
    isConnected: mcpConnected,
    sendMCPRequest,
    lastMessage,
    error: mcpError
  } = useMCPSSE({
    autoReconnect: true,
    maxReconnectAttempts: 3
  })

  useEffect(() => {
    // 加载 Checkout.com Frames SDK
    const script = document.createElement("script")
    script.src = "https://cdn.checkout.com/js/framesv2.min.js"
    script.async = true
    script.onload = initializeFrames
    document.head.appendChild(script)

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script)
      }
    }
  }, [])

  const initializeFrames = () => {
    if (framesInitialized.current || !window.Frames) return

    framesInitialized.current = true

    window.Frames.init({
      publicKey: process.env.NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY,
      style: {
        base: {
          fontSize: "16px",
          fontFamily: "system-ui, -apple-system, sans-serif",
          color: "#333",
          "::placeholder": {
            color: "#999",
          },
        },
        invalid: {
          color: "#e53e3e",
        },
        focus: {
          border: "2px solid #3182ce",
        },
      },
      modes: [
        {
          selector: ".card-number-frame",
          type: "card-number",
        },
        {
          selector: ".expiry-date-frame",
          type: "expiry-date",
        },
        {
          selector: ".cvv-frame",
          type: "cvv",
        },
      ],
    })

    // 监听 Frames 事件
    window.Frames.addEventHandler(window.Frames.Events.CARD_VALIDATION_CHANGED, (event: any) => {
      console.log("Card validation changed:", event)
      setCardValidation({
        cardNumber: event.isValid && event.element === "card-number",
        expiryDate: event.isValid && event.element === "expiry-date",
        cvv: event.isValid && event.element === "cvv",
      })

      // 检测卡片类型
      if (event.element === "card-number" && event.scheme) {
        setDetectedCardType(event.scheme)
      }
    })

    window.Frames.addEventHandler(window.Frames.Events.CARD_TOKENIZED, handleCardTokenized)

    window.Frames.addEventHandler(window.Frames.Events.CARD_TOKENIZATION_FAILED, (event: any) => {
      setError("卡片验证失败，请检查您的卡片信息")
      setIsLoading(false)
    })

    window.Frames.addEventHandler(window.Frames.Events.READY, () => {
      setIsFramesReady(true)
    })
  }

  const handleCardTokenized = async (event: FramesCardTokenizedEvent) => {
    try {
      const paymentData: PaymentRequest = {
        amount: customerInfo.amount,
        currency: "USD",
        reference: `order-${Date.now()}`,
        customer: {
          email: customerInfo.email,
          name: customerInfo.name,
        },
      }

      const response = await fetch("/api/payment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token: event.token,
          ...paymentData,
        }),
      })

      const result = await response.json()

      if (response.ok && result.approved) {
        onPaymentSuccess(result.id)
      } else {
        onPaymentError(result.response_summary || "支付失败")
      }
    } catch (error) {
      onPaymentError("支付处理过程中发生错误")
    } finally {
      setIsLoading(false)
    }
  }

  // 获取卡片类型图标
  const getCardIcon = (cardType: string | null) => {
    switch (cardType?.toLowerCase()) {
      case 'visa':
        return '💳'
      case 'mastercard':
        return '💳'
      case 'amex':
      case 'american express':
        return '💳'
      default:
        return <CreditCard className="h-4 w-4" />
    }
  }

  // 检查表单是否有效
  const isFormValid = () => {
    return (
      customerInfo.email &&
      customerInfo.name &&
      cardValidation.cardNumber &&
      cardValidation.expiryDate &&
      cardValidation.cvv
    )
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsLoading(true)

    if (!customerInfo.email || !customerInfo.name) {
      setError("请填写所有必填信息")
      setIsLoading(false)
      return
    }

    // 触发卡片令牌化
    window.Frames.submitCard()
  }

  return (
    <div className="w-full max-w-lg mx-auto">
      <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                <CreditCard className="h-8 w-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1">
                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle2 className="h-2.5 w-2.5 text-white" />
                </div>
              </div>
            </div>
          </div>
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            安全支付
          </CardTitle>
          <CardDescription className="text-gray-500 mt-2">
            使用企业级加密技术保护您的支付安全
          </CardDescription>
        </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 客户信息 */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">姓名 *</Label>
              <Input
                id="name"
                type="text"
                value={customerInfo.name}
                onChange={(e) => setCustomerInfo((prev) => ({ ...prev, name: e.target.value }))}
                placeholder="请输入您的姓名"
                required
              />
            </div>

            <div>
              <Label htmlFor="email">邮箱 *</Label>
              <Input
                id="email"
                type="email"
                value={customerInfo.email}
                onChange={(e) => setCustomerInfo((prev) => ({ ...prev, email: e.target.value }))}
                placeholder="请输入您的邮箱"
                required
              />
            </div>

            <div>
              <Label htmlFor="amount">金额 (USD)</Label>
              <Input
                id="amount"
                type="number"
                min="100"
                step="100"
                value={customerInfo.amount}
                onChange={(e) =>
                  setCustomerInfo((prev) => ({ ...prev, amount: Number.parseInt(e.target.value) || 1000 }))
                }
                placeholder="1000"
              />
              <p className="text-sm text-gray-500 mt-1">金额以分为单位 (1000 = $10.00)</p>
            </div>
          </div>

          {/* 卡片信息 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm text-gray-600">您的卡片信息经过加密保护</span>
            </div>

            <div>
              <Label>卡号 *</Label>
              <div className="card-number-frame border rounded-md p-3 min-h-[48px] bg-white"></div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>有效期 *</Label>
                <div className="expiry-date-frame border rounded-md p-3 min-h-[48px] bg-white"></div>
              </div>
              <div>
                <Label>CVV *</Label>
                <div className="cvv-frame border rounded-md p-3 min-h-[48px] bg-white"></div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Lock className="h-4 w-4" />
            <span>256位SSL加密保护</span>
          </div>
        </CardContent>

        <CardFooter>
          <Button type="submit" className="w-full" disabled={isLoading || !isFramesReady}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                处理中...
              </>
            ) : (
              `支付 $${(customerInfo.amount / 100).toFixed(2)}`
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
