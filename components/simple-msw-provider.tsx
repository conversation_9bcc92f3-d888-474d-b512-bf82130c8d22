'use client'

import { useEffect, useState } from 'react'

interface SimpleMSWProviderProps {
  children: React.ReactNode
}

export function SimpleMSWProvider({ children }: SimpleMSWProviderProps) {
  const [mswReady, setMswReady] = useState(false)

  useEffect(() => {
    const initMSW = async () => {
      if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
        try {
          console.log('🚀 Starting MSW initialization...')
          
          // 动态导入MSW模块
          const { setupWorker } = await import('msw/browser')
          const { http, HttpResponse } = await import('msw')
          
          // 定义处理器
          const handlers = [
            http.post('/v1/phone/signin', async ({ request }) => {
              console.log('🎯 MSW intercepted /v1/phone/signin request')
              const body = await request.json() as any
              return HttpResponse.json({
                success: true,
                message: 'MSW successfully intercepted request!',
                accessToken: 'mock_access_token_12345',
                refreshToken: 'mock_refresh_token_67890',
                user: {
                  id: 'user_123',
                  mobileNumber: body.mobileNumber,
                  countryCode: body.countryCode
                },
                timestamp: new Date().toISOString()
              })
            }),
            
            http.get('/api/test-msw', () => {
              console.log('🎯 MSW intercepted /api/test-msw request')
              return HttpResponse.json({
                message: 'MSW is working! This response is mocked.',
                timestamp: new Date().toISOString()
              })
            }),

            http.post('/v1/phone/code/send', async ({ request }) => {
              console.log('🎯 MSW intercepted /v1/phone/code/send request')
              const body = await request.json() as any
              return HttpResponse.json({
                success: true,
                message: "Verification code sent successfully",
                clientId: body.clientId || "100033",
                codeLength: 4,
                language: body.language || "zh-CN",
                countryCode: body.countryCode || 93,
                mobileNumber: body.mobileNumber,
                category: body.category || "Text"
              })
            })
          ]

          // 创建worker
          const worker = setupWorker(...handlers)
          
          // 启动MSW worker并等待完成
          await worker.start({
            serviceWorker: {
              url: '/mockServiceWorker.js'
            },
            onUnhandledRequest: 'bypass'
          })
          
          console.log('✅ MSW worker started successfully')
          
          // 将worker保存到全局变量，以便调试
          ;(window as any).__mswWorker = worker
          
        } catch (error) {
          console.error('❌ Failed to initialize MSW:', error)
        }
      } else {
        console.log('⏭️ Skipping MSW initialization (production or SSR)')
      }
      
      // 设置为ready
      setMswReady(true)
    }

    initMSW()
  }, [])

  // 始终渲染children，避免hydration不匹配
  return <>{children}</>
}
