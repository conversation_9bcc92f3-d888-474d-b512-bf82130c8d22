'use client'

import { useEffect, useState } from 'react'

interface MSWProviderProps {
  children: React.ReactNode
}

export function MSWProvider({ children }: MSWProviderProps) {
  const [mswReady, setMswReady] = useState(false)

  useEffect(() => {
    const initMSW = async () => {
      if (process.env.NODE_ENV === 'development') {
        try {
          console.log('Starting MSW initialization...')

          if (typeof window !== 'undefined') {
            // Browser environment
            const { worker } = await import('@/mocks/browser')
            await worker.start({
              onUnhandledRequest: 'bypass',
              serviceWorker: {
                url: '/mockServiceWorker.js'
              }
            })
            console.log('MSW worker started successfully')
          }
        } catch (error) {
          console.error('Failed to initialize MSW:', error)
        }
      }
      setMswReady(true)
    }

    initMSW()
  }, [])

  if (!mswReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Initializing MSW...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
