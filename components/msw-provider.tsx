// components/msw-provider.tsx
'use client'

import { useEffect, useState } from 'react'
import { handlers } from '@/mocks/handler'

export function MSWProvider({ children }: { children: React.ReactNode }) {
  const [mswReady, setMswReady] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    const initMSW = async () => {
      if (process.env.NODE_ENV !== 'development' && typeof window !== 'undefined') {
        try {
          const { setupWorker } = await import('msw/browser')
          const { http, HttpResponse } = await import('msw')
          
          

          const worker = setupWorker(...handlers)
          await worker.start({
            serviceWorker: { url: '/mockServiceWorker.js' },
            onUnhandledRequest: 'bypass'
          })
          
          console.log('✅ MSW initialized successfully')
        } catch (error) {
          console.error('❌ MSW initialization failed:', error)
        }
      }
      setMswReady(true)
    }

    if (isClient) {
      initMSW()
    }
  }, [isClient])

  if (!isClient) {
    return <>{children}</>
  }

  if (!mswReady) {
    return 'loading'
  }

  return <>{children}</>
}