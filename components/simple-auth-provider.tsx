'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useMemo, useCallback, useState, useEffect } from 'react'

interface SimpleAuthProviderProps {
  children: React.ReactNode
}

export function SimpleAuthProvider({ children }: SimpleAuthProviderProps) {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // 检查是否是认证错误
  const isAuthError = useCallback((error: Error) => {
    const message = error.message.toLowerCase()
    return (
      error.message === "AUTHENTICATION_REQUIRED" ||
      error.message === "AUTHENTICATION_FAILED" ||
      message.includes("401") ||
      message.includes("unauthorized") ||
      message.includes("token") ||
      message.includes("authentication") ||
      message.includes("forbidden") ||
      message.includes("403")
    )
  }, [])

  // 处理认证错误
  const handleAuthError = useCallback((error: Error) => {
    // 只在客户端挂载后处理
    if (!mounted) return
    
    console.error("🔐 Authentication error detected:", error.message)
    
    if (isAuthError(error)) {
      console.log("🚪 Clearing auth data and redirecting to login...")
      
      // 清除认证数据
      if (typeof window !== "undefined") {
        localStorage.removeItem("token")
        localStorage.removeItem("refreshToken")
        localStorage.removeItem("accessToken")
        localStorage.removeItem("user")
      }
      
      // 跳转到登录页面
      router.push("/login")
      
      console.log("Session expired. Please login again.")
    }
  }, [router, isAuthError, mounted])

  // 创建QueryClient
  const queryClient = useMemo(() => {
    return new QueryClient({
      defaultOptions: {
        queries: {
          retry: (failureCount, error) => {
            if (isAuthError(error as Error)) {
              return false
            }
            return failureCount < 2
          },
          staleTime: 1000 * 60 * 5,
          refetchOnWindowFocus: false,
          onError: handleAuthError,
        },
        mutations: {
          retry: (failureCount, error) => {
            if (isAuthError(error as Error)) {
              return false
            }
            return failureCount < 1
          },
          onError: handleAuthError,
        },
      },
    })
  }, [handleAuthError, isAuthError])

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
