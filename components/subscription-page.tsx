"use client";

import { useEffect, useState } from "react";
import { ChevronLeft, Check, X, LogOut, HelpCircle } from "lucide-react";
import { UserAvatar } from "./user-avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { plans, tabs, themeStyles } from "@/lib/themes";
import Image from "next/image";
import { usePhonePasswordLogin } from "@/hooks/login";
import { IPhonepassword } from "@/server/login";
export function SubscriptionPage() {
  const [selectedPlan, setSelectedPlan] = useState("1");
  const [activeTab, setActiveTab] = useState("vip");
  const [cardNumber, setCardNumber] = useState("1234 1234 1234 1234");
  const [cardholderName, setCardholderName] = useState("Jordan Smith");
  const [expiryDate, setExpiryDate] = useState("MM/YY");
  const [securityCode, setSecurityCode] = useState("CVV");

  const currentTheme = themeStyles[activeTab as keyof typeof themeStyles];

  const textUrlMap = {
    vip: {
      selected:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_text-selected.png",
      unSelected: {
        light:
          "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_text_light_unselected.png",
        dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_text_dark_unselected.png",
      },
      width: 35,
      height: 22,
    },
    see: {
      selected:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/see_text_selected.png",
      unSelected: {
        light:
          "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/see_text_light_unselected.png",
        dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/see_text_dark_unselected.png",
      },
      width: 43,
      height: 26,
    },
    premium: {
      selected:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/premium_text_selected.png",
      unSelected: {
        light:
          "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/premium_text_light_unselected.png",
        dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/premium_text_dark_unselected.png",
      },
      width: 99,
      height: 22,
    },
    ultra: {
      selected:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/ultra_text_selected.png",
      unSelected: {
        light:
          "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/ultra_text_light_unselected.png",
        dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/ultra_text_dark_unselected.png",
      },
      width: 103.33,
      height: 30,
    },
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    setSelectedPlan("1");
  };

  const selectedPlanData = plans.find((plan) => plan.id === selectedPlan);

  const phonePasswordLogin = usePhonePasswordLogin();
  const handlePhonePasswordLogin = async (data: IPhonepassword) => {
    phonePasswordLogin.mutate(data, {
      onSuccess: (data) => {
        console.log("登录成功！");
        // 处理登录成功的逻辑
      },
      onError: (error) => {
        console.log("登录失败，请检查账号密码");
      },
    });
  };

  useEffect(() => {
    handlePhonePasswordLogin({
      mobileNumber: "12345678901",
      password: "123456",
      clientId: "",
      code: 7149,
      countryCode: "",
      signinType: "",
      extra: {}
    });
  }, []);

  return (
    <div className="min-h-screen">
      {/* Mobile Layout - 保持不变 */}
      <div className={cn("md:hidden min-h-screen", currentTheme.background)}>
        {/* Mobile Header */}
        <div className="bg-gray-800 px-4 py-4 flex items-center justify-between">
          <ChevronLeft className="w-6 h-6 text-white" strokeWidth={2} />
          <div className="w-12 h-12 bg-gradient-to-b from-orange-400 to-orange-500 rounded-2xl flex items-center justify-center">
            <div className="text-white text-xl ">🦊</div>
          </div>
          <div className="w-6"></div>
        </div>

        {/* User Profile Section */}
        <div className="px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <UserAvatar
                src="/placeholder.svg?height=48&width=48"
                alt="Anita"
                size={48}
              />
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <span
                    className={cn("font-semibold text-lg", currentTheme.text)}
                  >
                    Anita
                  </span>
                  <span className="bg-orange-400 text-white text-xs px-2 py-1 rounded-md ">
                    {activeTab.toUpperCase()}
                  </span>
                </div>
                <div className={cn("text-sm", currentTheme.textSecondary)}>
                  VIP benefits expire 2025/09/10
                </div>
              </div>
            </div>
            <button
              className={cn(
                "text-sm font-medium px-3 py-2 rounded-lg bg-white/20",
                currentTheme.textSecondary
              )}
            >
              Log out
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-4 pb-8">
          <div className="flex gap-3">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={cn(
                  "px-4 py-3 rounded-2xl text-sm font-semibold transition-colors flex-1 text-center",
                  tab.id === activeTab
                    ? currentTheme.tabSelected
                    : currentTheme.tabUnselected
                )}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Icon */}
        <div className="flex justify-center pb-12">
          <div
            className={cn(
              "w-24 h-24 rounded-3xl flex items-center justify-center shadow-lg",
              currentTheme.iconBg
            )}
          >
            <div className="text-4xl">{currentTheme.icon}</div>
          </div>
        </div>

        {/* Subscription Plans */}
        <div className="px-4 pb-8">
          <div className="flex gap-4">
            {plans
              .filter((plan) => plan.id === activeTab)
              .concat(plans.filter((plan) => plan.id !== activeTab).slice(0, 2))
              .map((plan) => (
                <div
                  key={plan.id}
                  onClick={() => setSelectedPlan(plan.id)}
                  className={cn(
                    "flex-1 rounded-2xl cursor-pointer transition-all duration-200 relative shadow-md border-2",
                    selectedPlan === plan.id
                      ? cn(
                          currentTheme.selectedCard,
                          currentTheme.selectedCardBorder
                        )
                      : currentTheme.unselectedCard
                  )}
                >
                  {plan.discount && selectedPlan === plan.id && (
                    <div className="absolute -top-3 left-3 bg-yellow-400 text-gray-800 text-xs  px-3 py-1 rounded-full shadow-sm">
                      {plan.discount}
                    </div>
                  )}

                  {selectedPlan === plan.id ? (
                    <>
                      <div className="bg-white mx-2 mt-2 rounded-t-xl p-4 text-center">
                        <div className="text-4xl  text-gray-800">
                          {plan.months}
                        </div>
                        <div className="text-sm text-gray-500 mb-2">months</div>
                        <div className="text-sm text-orange-500 font-medium">
                          {plan.monthlyPrice}
                        </div>
                      </div>
                      <div
                        className={cn(
                          "mx-2 mb-2 rounded-b-xl p-4 text-center",
                          currentTheme.selectedCard
                        )}
                      >
                        <div className={cn("text-xl ", currentTheme.text)}>
                          {plan.totalPrice}
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="p-4 text-center">
                      <div className="text-4xl  text-gray-400">
                        {plan.months}
                      </div>
                      <div className="text-sm text-gray-400 mb-2">months</div>
                      <div className="text-sm text-gray-400 font-medium mb-3">
                        {plan.monthlyPrice}
                      </div>
                      <div className="text-xl  text-gray-400">
                        {plan.totalPrice}
                      </div>
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>

        {/* Features */}
        <div className="px-4 pb-8">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-center text-gray-400 text-sm mb-6 font-medium">
              {activeTab.toUpperCase()} Exclusive Privileges
            </h3>
            <div className="space-y-4">
              {currentTheme.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                    <Check className="w-5 h-5 text-white" strokeWidth={3} />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-800 text-lg mb-1">
                      {feature.name}
                    </div>
                    <div className="text-sm text-gray-500">{feature.desc}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pay Button */}
        <div className="px-4 pb-8">
          <Button
            className={cn(
              "w-full py-4  text-lg rounded-2xl shadow-md border-0",
              currentTheme.button
            )}
          >
            Pay
          </Button>
        </div>

        {/* Home Indicator */}
        <div className="flex justify-center pb-6">
          <div className="w-32 h-1 bg-black rounded-full"></div>
        </div>
      </div>

      {/* Desktop Layout  */}
      <div className={cn("hidden md:block min-h-screen bg-black")}>
        {/* 背景图 */}
        <div
          className={cn(
            "opacity-60 hidden md:block min-h-screen bg-[url('https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/desktop-bg.png')] bg-no-repeat bg-[length:100%_100%] "
          )}
        ></div>
        {/* 订阅页面 */}
        <div
          className={cn(
            "overflow-hidden h-[692px] w-[1024px] mx-auto pt-[24px] pl-[20px] pr-[20px] mb-[16px] absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2 rounded-[36px]",
            currentTheme.background
          )}
        >
          {/* ultra背景渐变 */}
          {activeTab === "ultra" && (
            <div className="bg-[url('https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/bg_ultra_linear.png')] absolute -top-[62px] -right-[19px] -z-10 w-[577px] h-[727px] bg-[length:100%_100%] bg-no-repeat"></div>
          )}
          {/* Header */}
          <div
            className={cn(
              "rounded-3xl h-[50px] mb-[25px] flex items-center justify-between",
              currentTheme.headerBg
            )}
          >
            <div className="flex items-center gap-4">
              <UserAvatar
                src="/placeholder.svg?height=60&width=60"
                alt="Anita"
                size={60}
              />
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <span
                    className={cn("text-[21px] font-medium", currentTheme.text)}
                  >
                    Anita
                  </span>
                  <span className="bg-yellow-400 text-white 800 text-sm px-3 py-1 rounded-full ">
                    VIP
                  </span>
                </div>
                <div
                  className={cn(
                    "text-sm text-gray-700 ",
                    currentTheme.textSecondary
                  )}
                >
                  Your
                  <span className="text-red-400"> VIP </span>
                  benefits will expire on
                  <span className="text-red-400"> 2025/09/10</span>. Renew or
                  upgrade now!
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <button className="flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600 transition-colors">
                <LogOut className="w-4 h-4" />
                Log out
              </button>
              <button
                className={cn("transition-colors", currentTheme.textSecondary)}
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          <div className="flex gap-8">
            {/* Left Content */}
            <div className="flex-1 w-[652px]">
              {/* Tab Navigation */}
              <div
                className={cn(
                  "flex gap-4 mb-[30px] bg-[rgba(255,255,255,0.4)] rounded-[20px] pl-1.5 pr-1.5",
                  currentTheme.tabUnselected
                )}
              >
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={cn(
                      "px-6 py-3 rounded-2xl font-semibold transition-colors w-[160px] h-[68px] flex items-center justify-center mt-1.5 mb-1.5 flex-1",
                      tab.id === activeTab
                        ? currentTheme.tabSelected
                        : currentTheme.tabUnselected
                    )}
                  >
                    {tab.id === activeTab ? (
                      <Image
                        alt="Ultra"
                        key={tab.id}
                        height={
                          textUrlMap[tab.id as keyof typeof textUrlMap].height
                        }
                        width={
                          textUrlMap[tab.id as keyof typeof textUrlMap].width
                        }
                        src={
                          textUrlMap[tab.id as keyof typeof textUrlMap].selected
                        }
                        className=" object-cover"
                      ></Image>
                    ) : (
                      <Image
                        alt="Ultra"
                        layout="auto"
                        key={tab.id}
                        height={
                          textUrlMap[tab.id as keyof typeof textUrlMap].height
                        }
                        width={
                          textUrlMap[tab.id as keyof typeof textUrlMap].width
                        }
                        src={
                          ["vip", "see"].includes(activeTab)
                            ? textUrlMap[tab.id as keyof typeof textUrlMap]
                                .unSelected.light
                            : textUrlMap[tab.id as keyof typeof textUrlMap]
                                .unSelected.dark
                        }
                        className=" object-cover"
                      ></Image>
                    )}
                  </button>
                ))}
              </div>

              {/* Subscription Plans */}
              <div className="grid grid-cols-3 gap-6 mb-4 h-[216px] box-content">
                {plans.map((plan) => (
                  <div
                    key={plan.id}
                    onClick={() => setSelectedPlan(plan.id)}
                    className={cn(
                      "h-full rounded-xl cursor-pointer transition-all duration-300 relative border-primary border-[4px] ",
                      selectedPlan === plan.id
                        ? cn(currentTheme.selectedCardBorder)
                        : cn(
                            currentTheme.unSelectedCardBorder,
                            currentTheme.cardBackground
                          )
                    )}
                  >
                    {plan.discount && selectedPlan === plan.id && (
                      <div
                        className={cn(
                          "w-[112.5px] h-[33.33px] absolute -translate-y-1/2 -translate-x-1/2 left-1/2  text-xs  px-3 py-1 rounded-full z-10 flex justify-center  items-center",
                          currentTheme.selectedCard,
                          selectedPlan === plan.id
                            ? currentTheme.planSelectText
                            : currentTheme.planUnSelectText,
                          selectedPlan === plan.id
                            ? currentTheme.selectedCardBorder
                            : currentTheme.unSelectedCardBorder,
                          selectedPlan === plan.id
                            ? currentTheme.selectedCard
                            : currentTheme.unselectedCard
                        )}
                      >
                        {plan.discount}
                      </div>
                    )}

                    {selectedPlan === plan.id ? (
                      <>
                        {/* Selected State */}
                        <div
                          className={cn(
                            "p-6 text-center rounded-xl",
                            currentTheme.cardBackground
                          )}
                        >
                          <div
                            className={cn(
                              "text-5xl  text-gray-800 mb-2",
                              currentTheme.text
                            )}
                          >
                            {plan.months}
                          </div>
                          <div
                            className={cn(
                              "text-sm text-gray-500 mb-3",
                              currentTheme.textSecondary
                            )}
                          >
                            months
                          </div>
                          <div
                            className={cn(
                              "text-s  font-medium",
                              currentTheme.planPriceText
                            )}
                          >
                            {plan.monthlyPrice}
                          </div>
                        </div>
                        <div
                          className={cn(
                            "p-6 text-center h-[70px]",
                            currentTheme.selectedCard
                          )}
                        >
                          <div
                            className={cn(
                              "text-2xl",
                              currentTheme.priceTextSelected
                            )}
                          >
                            {plan.totalPrice}
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="p-6 text-center">
                        <div className={cn("text-5xl mb-2", currentTheme.text)}>
                          {plan.months}
                        </div>
                        <div
                          className={cn(
                            "text-sm text-gray-800 mb-3",
                            currentTheme.text
                          )}
                        >
                          months
                        </div>
                        <div
                          className={cn(
                            "text-sm font-medium mb-4",
                            currentTheme.planPriceText
                          )}
                        >
                          {plan.monthlyPrice}
                        </div>
                        <div
                          className={cn(
                            "text-2xl text-gray-800",
                            currentTheme.priceTextUnSelected
                          )}
                        >
                          {plan.totalPrice}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Terms */}
              <div
                className={cn(
                  "text-xs mb-2 mt-4 inline-block ",
                  currentTheme.textSecondary
                )}
              >
                Auto-renewable subscription, cancel anytime in App Store
                settings, automatic renewal 24 hours before the subscription
                period ends. By clicking "Continue", you agree to and accept our{" "}
                <span className="text-blue-500 underline cursor-pointer">
                  Terms of Service
                </span>{" "}
                and{" "}
                <span className="text-blue-500 underline cursor-pointer">
                  Privacy Policy
                </span>
                .
              </div>

              {/* Special Feature for SEE */}
              {currentTheme.specialFeature && (
                <div className="mb-8 flex justify-center">
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="relative">
                        {currentTheme.specialFeature.avatars.map(
                          (avatar, index) => (
                            <div
                              key={index}
                              className={cn(
                                "absolute w-16 h-16 rounded-full border-4 border-white overflow-hidden",
                                index === 0 && "left-0 z-30",
                                index === 1 && "left-8 z-20",
                                index === 2 && "left-16 z-10"
                              )}
                            >
                              <img
                                src={avatar || "/placeholder.svg"}
                                alt="User"
                                className="w-full h-full object-cover"
                              />
                            </div>
                          )
                        )}
                        <div className="w-24 h-16"></div>
                        <div className="absolute bottom-0 right-0 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-lg">🦊</span>
                        </div>
                      </div>
                    </div>
                    <h3 className={cn("text-xl  mb-2", currentTheme.text)}>
                      {currentTheme.specialFeature.title}
                    </h3>
                    <p className={cn("text-sm", currentTheme.textSecondary)}>
                      {currentTheme.specialFeature.desc}
                    </p>
                  </div>
                </div>
              )}

              {/* Features Table */}
              <div
                className={cn(
                  "rounded-2xl overflow-scroll max-h-[199px] w-full"
                )}
              >
                {/* <div className="grid grid-cols-4 gap-0">
                
                  <div className="p-4">
                    <span className={cn("font-semibold", currentTheme.text)}>
                      Features
                    </span>
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.featuresHeaderBg,
                      "text-white"
                    )}
                  >
                    VIP
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.text
                    )}
                  >
                    Premium
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.text
                    )}
                  >
                    Ultra Premium
                  </div>

                  {currentTheme.features.map((feature, index) => (
                    <div
                      key={index}
                      className="col-span-4 grid grid-cols-4 gap-0 border-t border-gray-200/20"
                    >
                      <div className="p-4">
                        <div className={cn("font-medium", currentTheme.text)}>
                          {feature.name}
                        </div>
                        <div
                          className={cn("text-sm", currentTheme.textSecondary)}
                        >
                          {feature.desc}
                        </div>
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                    </div>
                  ))}
                </div> */}

                <Image
                  alt="feature"
                  height={1355}
                  width={652}
                  src={
                    "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_feature.png"
                  }
                  className="w-full"
                ></Image>
              </div>
            </div>

            {/* Right Payment Panel */}
            <div className="w-80">
              <div className={cn("rounded-2xl p-4", currentTheme.paymentBg)}>
                <div className="mb-6">
                  <div className="text-sm text-gray-500 mb-1">
                    Subscribe to Tantan
                  </div>
                  <div className="text-2xl text-gray-800">
                    US${selectedPlanData?.totalPrice || "618"}
                  </div>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">小计</span>
                    <span className="text-gray-800">US$20.00</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 flex items-center gap-1">
                      税 <HelpCircle className="w-3 h-3" />
                    </span>
                    <span className="text-gray-500">US$0.00</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold">
                      <span className="text-gray-800">今日应付合计</span>
                      <span className="text-gray-800">US$20.00</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <RadioGroup
                    defaultValue="card"
                    className="flex items-center gap-2"
                  >
                    <RadioGroupItem value="card" id="card" />
                    <Label
                      htmlFor="card"
                      className="flex items-center gap-2 text-gray-800"
                    >
                      <div className="w-4 h-4 border border-gray-300 rounded flex items-center justify-center">
                        <div className="w-2 h-2 bg-black rounded-full"></div>
                      </div>
                      Card
                    </Label>
                  </RadioGroup>

                  <div>
                    <Label className="text-sm text-gray-700">
                      Cardholder name
                    </Label>
                    <Input
                      value={cardholderName}
                      onChange={(e) => setCardholderName(e.target.value)}
                      className="mt-1 bg-gray-50 border-gray-200"
                    />
                  </div>

                  <div>
                    <Label className="text-sm text-gray-700">Card number</Label>
                    <div className="relative">
                      <Input
                        value={cardNumber}
                        onChange={(e) => setCardNumber(e.target.value)}
                        className="mt-1 pr-20 bg-gray-50 border-gray-200"
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-1">
                        <div className="w-6 h-4 bg-blue-600 rounded text-white text-xs flex items-center justify-center ">
                          V
                        </div>
                        <div className="w-6 h-4 bg-red-600 rounded"></div>
                        <div className="w-6 h-4 bg-blue-500 rounded"></div>
                        <div className="w-6 h-4 bg-orange-500 rounded"></div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm text-gray-700">
                        Expiry date
                      </Label>
                      <Input
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                        className="mt-1 bg-gray-50 border-gray-200"
                      />
                    </div>
                    <div>
                      <Label className="text-sm text-gray-700 flex items-center gap-1">
                        Security code
                        <HelpCircle className="w-3 h-3" />
                      </Label>
                      <Input
                        value={securityCode}
                        onChange={(e) => setSecurityCode(e.target.value)}
                        className="mt-1 bg-gray-50 border-gray-200"
                      />
                    </div>
                  </div>

                  <Button className="w-full bg-black text-white hover:bg-gray-800 py-3 rounded-lg font-semibold">
                    pay
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
