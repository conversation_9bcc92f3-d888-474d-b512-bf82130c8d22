'use client'

import { useState, useEffect } from 'react'
import { SimpleMSWProvider } from './simple-msw-provider'
import { SimpleAuthProvider } from './simple-auth-provider'

interface ClientProvidersProps {
  children: React.ReactNode
}

export function ClientProviders({ children }: ClientProvidersProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // 在服务端渲染时，返回最简单的结构
  if (!isClient) {
    return <>{children}</>
  }

  // 客户端渲染时，使用完整的Provider链
  return (
    <SimpleMSWProvider>
      <SimpleAuthProvider>
        {children}
      </SimpleAuthProvider>
    </SimpleMSWProvider>
  )
}
