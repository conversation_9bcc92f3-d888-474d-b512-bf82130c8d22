'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export function TestMSW() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testMSWEndpoint = async () => {
    setLoading(true)
    setResult('')

    try {
      console.log('Testing MSW with /api/test-msw endpoint...')

      const response = await fetch('/api/test-msw', {
        method: 'GET',
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('Response data:', data)
      setResult(`Success: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      console.error('Test failed:', error)
      setResult(`Error: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setLoading(false)
    }
  }

  const testPhoneSignin = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing /v1/phone/signin endpoint...')
      
      const response = await fetch('/v1/phone/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mobileNumber: '1234567890',
          password: 'test123',
          clientId: '100033',
          countryCode: 1,
          signinType: 'password',
          extra: {}
        })
      })
      
      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      console.log('Response data:', data)
      setResult(`Success: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      console.error('Test failed:', error)
      setResult(`Error: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setLoading(false)
    }
  }

  const testPhoneCodeSend = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing /v1/phone/code/send endpoint...')
      
      const response = await fetch('/v1/phone/code/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mobileNumber: '1234567890',
          countryCode: 1,
          clientId: '100033',
          category: 'Text',
          codeLength: 4,
          language: 'zh-CN'
        })
      })
      
      console.log('Response status:', response.status)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      console.log('Response data:', data)
      setResult(`Success: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      console.error('Test failed:', error)
      setResult(`Error: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">MSW Test</h2>
      
      <div className="space-y-4">
        <div className="flex gap-4">
          <Button
            onClick={testMSWEndpoint}
            disabled={loading}
            variant="outline"
          >
            Test MSW Basic
          </Button>

          <Button
            onClick={testPhoneSignin}
            disabled={loading}
            variant="outline"
          >
            Test Phone Signin
          </Button>

          <Button
            onClick={testPhoneCodeSend}
            disabled={loading}
            variant="outline"
          >
            Test Phone Code Send
          </Button>
        </div>
        
        {loading && (
          <div className="text-blue-600">Testing...</div>
        )}
        
        {result && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Result:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {result}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
