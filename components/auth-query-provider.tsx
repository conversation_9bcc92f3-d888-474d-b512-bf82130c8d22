'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useEffect, useMemo } from 'react'

interface AuthQueryProviderProps {
  children: React.ReactNode
}

export function AuthQueryProvider({ children }: AuthQueryProviderProps) {
  const router = useRouter()

  // 创建带有路由功能的QueryClient
  const queryClient = useMemo(() => {
    const handleAuthError = (error: Error) => {
      console.error("Query error:", error.message)
      
      // 检查是否是认证相关错误
      if (
        error.message === "AUTHENTICATION_REQUIRED" ||
        error.message === "AUTHENTICATION_FAILED" ||
        error.message.includes("401") ||
        error.message.includes("Unauthorized")
      ) {
        // 清除本地存储的token
        if (typeof window !== "undefined") {
          localStorage.removeItem("token")
          localStorage.removeItem("refreshToken")
        }
        
        // 使用Next.js路由跳转
        router.push("/login")
      }
    }

    return new QueryClient({
      defaultOptions: {
        queries: {
          retry: (failureCount, error) => {
            // 认证错误不重试
            if (
              error.message === "AUTHENTICATION_REQUIRED" ||
              error.message === "AUTHENTICATION_FAILED"
            ) {
              return false
            }
            return failureCount < 2
          },
          staleTime: 1000 * 60 * 5, // 数据缓存5分钟
          onError: handleAuthError,
        },
        mutations: {
          onError: handleAuthError,
        },
      },
    })
  }, [router])

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
