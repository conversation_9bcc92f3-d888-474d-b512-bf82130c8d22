'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useMemo, useCallback } from 'react'

interface AuthQueryProviderProps {
  children: React.ReactNode
}

export function AuthQueryProvider({ children }: AuthQueryProviderProps) {
  const router = useRouter()

  // 清除认证信息的函数
  const clearAuthData = useCallback(() => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("token")
      localStorage.removeItem("refreshToken")
      localStorage.removeItem("accessToken")
      // 清除其他可能的认证相关数据
      localStorage.removeItem("user")
    }
  }, [])

  // 检查是否是认证错误
  const isAuthError = useCallback((error: Error) => {
    const message = error.message.toLowerCase()
    return (
      error.message === "AUTHENTICATION_REQUIRED" ||
      error.message === "AUTHENTICATION_FAILED" ||
      message.includes("401") ||
      message.includes("unauthorized") ||
      message.includes("token") ||
      message.includes("authentication") ||
      message.includes("forbidden") ||
      message.includes("403")
    )
  }, [])

  // 处理认证错误
  const handleAuthError = useCallback((error: Error) => {
    console.error("🔐 Authentication error detected:", error.message)

    if (isAuthError(error)) {
      console.log("🚪 Clearing auth data and redirecting to login...")

      // 清除认证数据
      clearAuthData()

      // 跳转到登录页面
      router.push("/login")

      // 可选：显示提示信息
      if (typeof window !== "undefined") {
        // 你可以在这里添加 toast 通知
        console.log("Session expired. Please login again.")
      }
    }
  }, [router, clearAuthData, isAuthError])

  // 创建带有路由功能的QueryClient
  const queryClient = useMemo(() => {
    return new QueryClient({
      defaultOptions: {
        queries: {
          // retry: (failureCount, error) => {
          //   // 认证错误不重试
          //   if (isAuthError(error as Error)) {
          //     return false
          //   }
          //   // 其他错误重试2次
          //   return failureCount < 2
          // },
          staleTime: 1000 * 60 * 5, // 数据缓存5分钟
          refetchOnWindowFocus: false, // 窗口聚焦时不自动重新获取
          onError: handleAuthError,
        },
        mutations: {
          // retry: (failureCount, error) => {
          //   // 认证错误不重试
          //   if (isAuthError(error as Error)) {
          //     return false
          //   }
          //   return failureCount < 1
          // },
          onError: handleAuthError,
        },
      },
    })
  }, [handleAuthError, isAuthError])

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
