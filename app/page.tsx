"use client";

import { useState, useEffect } from "react";
import { SubscriptionPage } from "@/components/subscription-page";
import { PerfectPhoneRegistration } from "@/components/register/PhoneRegister";
import { TestMSW } from "@/components/test-msw";
import { But<PERSON> } from "@/components/ui/button";
import type { ThemeKey } from "@/lib/themes";
import { MobileSubscriptionPage } from "@/components/subscribe/mobile-subscribe";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/hooks/login";
export default function HomePage() {
  const [currentTheme, setCurrentTheme] = useState<ThemeKey>("vip");

  useEffect(() => {
    const initMSW = async () => {
      if (process.env.NODE_ENV === 'development') {
        try {
          console.log('Initializing MSW...');

          if (typeof window !== 'undefined') {
            // Browser environment
            const { worker } = await import('@/mocks/browser');
            await worker.start({
              onUnhandledRequest: 'bypass',
              serviceWorker: {
                url: '/mockServiceWorker.js'
              }
            });
            console.log('MSW worker started successfully');
          }
        } catch (error) {
          console.error('Failed to initialize MSW:', error);
        }
      }
    };

    initMSW();
  }, []);

  return (
    <div>
      <QueryClientProvider client={queryClient}>
        {/* <TestMSW /> */}
        <SubscriptionPage />
      </QueryClientProvider>
      {/* <PerfectPhoneRegistration /> */}
    </div>
  );
}
