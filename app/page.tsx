"use client";

import { useState, useEffect } from "react";
import { SubscriptionPage } from "@/components/subscription-page";
import { PerfectPhoneRegistration } from "@/components/register/PhoneRegister";
import { Button } from "@/components/ui/button";
import type { ThemeKey } from "@/lib/themes";
import { MobileSubscriptionPage } from "@/components/subscribe/mobile-subscribe";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/hooks/login";
export default function HomePage() {
  const [currentTheme, setCurrentTheme] = useState<ThemeKey>("vip");

  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      import("@/mocks/initMock").then(({ initMocks }) => initMocks());
    }
  }, []);

  return (
    <div>
      <QueryClientProvider client={queryClient}>
        <SubscriptionPage />
      </QueryClientProvider>
      {/* <PerfectPhoneRegistration /> */}
    </div>
  );
}
