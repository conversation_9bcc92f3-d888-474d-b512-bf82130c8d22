"use client";

import { useState, useEffect } from "react";
import { SubscriptionPage } from "@/components/subscription-page";
import { PerfectPhoneRegistration } from "@/components/register/PhoneRegister";
import { Button } from "@/components/ui/button";
import type { ThemeKey } from "@/lib/themes";
import { MobileSubscriptionPage } from "@/components/subscribe/mobile-subscribe";
import { QueryClientProvider } from "@tanstack/react-query";
import {
  queryClient,
  usePhoneCodeSend,
  usePhonePasswordLogin,
} from "@/hooks/queryHook";
import { set } from "react-hook-form";
import { initMocks } from "@/mocks/initMock";
import { IPhonepassword, phonePasswordLogin } from "@/server/checkout";
export default function HomePage() {
  const [currentTheme, setCurrentTheme] = useState<ThemeKey>("vip");
  const [mockEnabled, setMockEnabled] = useState(false);

  const phonePasswordLogin = usePhonePasswordLogin();
  const phoneCodeSend = usePhoneCodeSend();

  const loadGeetestScript = async () => {
    return new Promise<void>((resolve, reject) => {
      if (document.getElementById("geetest-script")) {
        resolve();
        return;
      }
      const script = document.createElement("script");
      script.src = "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/gt4.js";
      script.id = "geetest-script";
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error("极验JS加载失败"));
      document.head.appendChild(script);
    });
  };
  const loadCheckoutScript = async () => {
    return new Promise<void>((resolve, reject) => {
      if ((window as any).Frames) {
        resolve();
        return;
      }
      const script = document.createElement("script");
      script.src =
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/framesv2.min.js";
      script.id = "checkout-script";
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error("checkoutJS加载失败"));
      document.head.appendChild(script);
    });
  };

  // 修复类型报错，给 window 增加类型声明，并为 captcha 参数添加类型
  const init = async () => {
    loadCheckoutScript();
    await loadGeetestScript();
    (window as any).initGeetest4(
      {
        captchaId: "dcfe08f2caf52629be6605880d3ef7fe",
        product: "bind", // 展现形式
        hideSuccess: true, // 是否隐藏验证成功弹窗
        mask: {
          outside: false, // 阻止点击蒙层关闭弹窗
          bgColor: "transparent",
        },
      },
      function (captcha: any) {
        // captcha为验证码实例

        // captcha.onReady(() => {
        //   captcha.showCaptcha();
        // });

        captcha.onSuccess(() => {
          try {
            const res = captcha.getValidate();
            console.log("validate", res);
          } catch (error) {
            console.log("error", error);
          }
        });

        // document.addEventListener("click", (e) => {
        //   // captcha.showCaptcha();

        //   const res = captcha.getValidate();
        //   console.log("validate", res);
        // });

        captcha.onClose(() => {});
      }
    );
    // const mockEnabled = await initMocks();
    const mockEnabled = true;
    // console.log("Mocks enabled:", mockEnabled);
    setMockEnabled(mockEnabled);
  };

  const handlePhonePasswordLogin = async (data: IPhonepassword) => {
    try {
      phonePasswordLogin.mutate(data, {
        onSuccess: (data) => {
          console.log("data", data);
          console.log("登录成功！");
          // 处理登录成功的逻辑
        },
        onError: (error) => {
          console.log("登录失败，请检查账号密码");
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handlePhoneCodeSend = async (data: IPhonepassword) => {
    try {
      phonePasswordLogin.mutate(data, {
        onSuccess: (data) => {
          console.log("data", data);
          console.log("登录成功！");
          // 处理登录成功的逻辑
        },
        onError: (error) => {
          console.log("登录失败，请检查账号密码");
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    // 接口mock
    // const initMSW = async () => {
    //   if (process.env.NODE_ENV === "development") {
    //     try {
    //       console.log("Initializing MSW...");

    //       if (typeof window !== "undefined") {
    //         // Browser environment
    //         const { worker } = await import("@/mocks/browser");
    //         await worker.start({
    //           onUnhandledRequest: "bypass",
    //           serviceWorker: {
    //             url: "/mockServiceWorker.js",
    //           },
    //         });

    //         setMockEnabled(true);
    //         console.log("MSW worker started successfully");
    //       }
    //     } catch (error) {
    //       console.error("Failed to initialize MSW:", error);
    //     }
    //   }
    // };

    // initMSW();

    handlePhonePasswordLogin({
      code: 7169,
      password: "123456",
      countryCode: 93,
      mobileNumber: "9870987",
      signinType: "password",
      extra: {},
      clientId: "100033",
    });

    init();
  }, []);

  return (
    <div>
      {process.env.NODE_ENV == "development" ? (
        mockEnabled && (
          <QueryClientProvider client={queryClient}>
            <PerfectPhoneRegistration />
          </QueryClientProvider>
        )
      ) : (
        <QueryClientProvider client={queryClient}>
          <PerfectPhoneRegistration />
        </QueryClientProvider>
      )}
    </div>
  );
}
