import type { Metada<PERSON> } from "next";
import "./globals.css";
import { MSWProvider } from "@/components/msw-provider";
import { queryClient } from "@/hooks/queryHook";
import { QueryClientProvider } from "@tanstack/react-query";

export const metadata: Metadata = {
  title: "Tantan ",
  description: "Tantan",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
      </head>
      <body>
        {
          <MSWProvider>
            <QueryClientProvider client={queryClient}>
              {children}
            </QueryClientProvider>
          </MSWProvider>
        }
      </body>
    </html>
  );
}
