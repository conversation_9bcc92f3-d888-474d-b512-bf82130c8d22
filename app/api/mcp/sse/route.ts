import { NextRequest } from "next/server"

// SSE 连接管理
const connections = new Map<string, ReadableStreamDefaultController>()

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const clientId = searchParams.get('clientId') || `client-${Date.now()}`

  // 创建 SSE 流
  const stream = new ReadableStream({
    start(controller) {
      // 存储连接
      connections.set(clientId, controller)
      
      // 发送初始连接消息
      const initMessage = {
        type: 'connection',
        data: {
          clientId,
          timestamp: new Date().toISOString(),
          message: 'MCP SSE connection established'
        }
      }
      
      controller.enqueue(`data: ${JSON.stringify(initMessage)}\n\n`)
      
      // 发送心跳
      const heartbeat = setInterval(() => {
        try {
          controller.enqueue(`data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`)
        } catch (error) {
          clearInterval(heartbeat)
          connections.delete(clientId)
        }
      }, 30000) // 30秒心跳
      
      // 清理函数
      request.signal.addEventListener('abort', () => {
        clearInterval(heartbeat)
        connections.delete(clientId)
        try {
          controller.close()
        } catch (error) {
          // 连接已关闭
        }
      })
    },
    
    cancel() {
      connections.delete(clientId)
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  })
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { clientId, message, type = 'message' } = body
    
    if (!clientId) {
      return Response.json({ error: 'clientId is required' }, { status: 400 })
    }
    
    // 获取连接
    const controller = connections.get(clientId)
    
    if (!controller) {
      return Response.json({ error: 'Client not connected' }, { status: 404 })
    }
    
    // 发送消息
    const sseMessage = {
      type,
      data: message,
      timestamp: new Date().toISOString()
    }
    
    try {
      controller.enqueue(`data: ${JSON.stringify(sseMessage)}\n\n`)
      return Response.json({ success: true, message: 'Message sent' })
    } catch (error) {
      connections.delete(clientId)
      return Response.json({ error: 'Failed to send message' }, { status: 500 })
    }
    
  } catch (error) {
    console.error('MCP SSE POST error:', error)
    return Response.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// 广播消息到所有连接的客户端
export function broadcastMessage(message: any, type: string = 'broadcast') {
  const sseMessage = {
    type,
    data: message,
    timestamp: new Date().toISOString()
  }
  
  const messageString = `data: ${JSON.stringify(sseMessage)}\n\n`
  
  connections.forEach((controller, clientId) => {
    try {
      controller.enqueue(messageString)
    } catch (error) {
      // 连接已断开，移除
      connections.delete(clientId)
    }
  })
}

// 发送消息到特定客户端
export function sendToClient(clientId: string, message: any, type: string = 'message') {
  const controller = connections.get(clientId)
  
  if (!controller) {
    return false
  }
  
  const sseMessage = {
    type,
    data: message,
    timestamp: new Date().toISOString()
  }
  
  try {
    controller.enqueue(`data: ${JSON.stringify(sseMessage)}\n\n`)
    return true
  } catch (error) {
    connections.delete(clientId)
    return false
  }
}
