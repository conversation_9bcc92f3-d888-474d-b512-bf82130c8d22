import { NextRequest, NextResponse } from "next/server"
import { broadcastMessage, sendToClient } from "../sse/route"

// MCP 服务器配置
interface MCPServerConfig {
  name: string
  version: string
  capabilities: string[]
}

const MCP_CONFIG: MCPServerConfig = {
  name: "checkout-payment-mcp",
  version: "1.0.0",
  capabilities: [
    "payment_processing",
    "real_time_updates", 
    "transaction_monitoring",
    "error_handling"
  ]
}

// MCP 消息类型
interface MCPMessage {
  id: string
  method: string
  params?: any
  clientId?: string
}

interface MCPResponse {
  id: string
  result?: any
  error?: {
    code: number
    message: string
    data?: any
  }
}

export async function GET(request: NextRequest) {
  // 返回 MCP 服务器信息
  return NextResponse.json({
    server: MCP_CONFIG,
    endpoints: {
      sse: "/api/mcp/sse",
      server: "/api/mcp/server"
    },
    status: "active",
    timestamp: new Date().toISOString()
  })
}

export async function POST(request: NextRequest) {
  try {
    const message: MCPMessage = await request.json()
    
    if (!message.id || !message.method) {
      return NextResponse.json({
        id: message.id || "unknown",
        error: {
          code: -32600,
          message: "Invalid Request: missing id or method"
        }
      } as MCPResponse, { status: 400 })
    }
    
    // 处理不同的 MCP 方法
    const response = await handleMCPMethod(message)
    
    // 如果有客户端ID，通过SSE发送响应
    if (message.clientId) {
      sendToClient(message.clientId, response, 'mcp_response')
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('MCP Server error:', error)
    return NextResponse.json({
      id: "unknown",
      error: {
        code: -32603,
        message: "Internal error",
        data: error instanceof Error ? error.message : String(error)
      }
    } as MCPResponse, { status: 500 })
  }
}

async function handleMCPMethod(message: MCPMessage): Promise<MCPResponse> {
  const { id, method, params } = message
  
  switch (method) {
    case "initialize":
      return {
        id,
        result: {
          protocolVersion: "2024-11-05",
          capabilities: MCP_CONFIG.capabilities,
          serverInfo: MCP_CONFIG
        }
      }
      
    case "payment.process":
      return await handlePaymentProcess(id, params)
      
    case "payment.status":
      return await handlePaymentStatus(id, params)
      
    case "payment.cancel":
      return await handlePaymentCancel(id, params)
      
    case "notifications.subscribe":
      return handleNotificationSubscribe(id, params)
      
    case "ping":
      return {
        id,
        result: {
          pong: true,
          timestamp: new Date().toISOString()
        }
      }
      
    default:
      return {
        id,
        error: {
          code: -32601,
          message: `Method not found: ${method}`
        }
      }
  }
}

async function handlePaymentProcess(id: string, params: any): Promise<MCPResponse> {
  try {
    // 这里集成实际的支付处理逻辑
    const { token, amount, currency, reference, customer, clientId } = params
    
    // 发送处理开始通知
    if (clientId) {
      sendToClient(clientId, {
        type: 'payment_processing_started',
        reference,
        amount,
        currency
      }, 'payment_update')
    }
    
    // 调用现有的支付API
    const paymentResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/payment`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        token,
        amount,
        currency,
        reference,
        customer,
      }),
    })
    
    const result = await paymentResponse.json()
    
    // 发送处理完成通知
    if (clientId) {
      sendToClient(clientId, {
        type: 'payment_processing_completed',
        result,
        success: paymentResponse.ok
      }, 'payment_update')
    }
    
    return {
      id,
      result: {
        success: paymentResponse.ok,
        payment: result,
        timestamp: new Date().toISOString()
      }
    }
    
  } catch (error) {
    return {
      id,
      error: {
        code: -32000,
        message: "Payment processing failed",
        data: error instanceof Error ? error.message : String(error)
      }
    }
  }
}

async function handlePaymentStatus(id: string, params: any): Promise<MCPResponse> {
  const { paymentId } = params
  
  // 这里应该查询实际的支付状态
  // 目前返回模拟数据
  return {
    id,
    result: {
      paymentId,
      status: "completed", // 或 "pending", "failed", "cancelled"
      timestamp: new Date().toISOString()
    }
  }
}

async function handlePaymentCancel(id: string, params: any): Promise<MCPResponse> {
  const { paymentId, reason } = params
  
  // 这里应该实现实际的支付取消逻辑
  return {
    id,
    result: {
      paymentId,
      cancelled: true,
      reason,
      timestamp: new Date().toISOString()
    }
  }
}

function handleNotificationSubscribe(id: string, params: any): Promise<MCPResponse> {
  const { clientId, events } = params
  
  // 订阅通知
  return Promise.resolve({
    id,
    result: {
      subscribed: true,
      clientId,
      events: events || ['payment_updates', 'system_notifications'],
      timestamp: new Date().toISOString()
    }
  })
}
