import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Phone signin request received:', body)
    
    // Mock response for phone signin
    if (body.signinType === 'password') {
      // Password login
      return NextResponse.json({
        success: true,
        accessToken: "mock_access_token_12345",
        refreshToken: "mock_refresh_token_67890",
        expiresIn: 3600,
        user: {
          id: "user_123",
          mobileNumber: body.mobileNumber,
          countryCode: body.countryCode
        }
      }, { status: 200 })
    } else {
      // Code verification or other signin types
      return NextResponse.json({
        success: true,
        message: "Verification code sent successfully",
        clientId: "100033",
        captcha: {
          captchaID: "",
          captchaOutput: "",
          genTime: "",
          lotNumber: "",
          passToken: "",
        },
        category: "Text",
        codeLength: 4,
        language: "zh-CN",
        countryCode: body.countryCode || 93,
        mobileNumber: body.mobileNumber || "1234001",
      }, { status: 200 })
    }
  } catch (error) {
    console.error('Error processing phone signin:', error)
    return NextResponse.json({
      success: false,
      error: "Invalid request format"
    }, { status: 400 })
  }
}
