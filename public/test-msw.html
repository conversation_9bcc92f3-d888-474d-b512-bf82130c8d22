<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSW Test</title>
</head>
<body>
    <h1>MSW Test Page</h1>
    <button id="testBtn">Test MSW</button>
    <div id="result"></div>

    <script type="module">
        async function initMSW() {
            if (process.env.NODE_ENV === 'development') {
                const { setupWorker } = await import('https://unpkg.com/msw@2.10.2/lib/browser/index.js');
                const { http, HttpResponse } = await import('https://unpkg.com/msw@2.10.2/lib/core/index.js');
                
                const handlers = [
                    http.post("/v1/phone/signin", async ({ request }) => {
                        console.log('MSW: Intercepted /v1/phone/signin');
                        const body = await request.json();
                        return HttpResponse.json({
                            success: true,
                            message: "MS<PERSON> is working!",
                            received: body,
                            timestamp: new Date().toISOString()
                        });
                    }),
                ];

                const worker = setupWorker(...handlers);
                await worker.start({
                    onUnhandledRequest: 'bypass',
                    serviceWorker: {
                        url: '/mockServiceWorker.js'
                    }
                });
                console.log('MSW worker started');
            }
        }

        async function testAPI() {
            try {
                const response = await fetch('/v1/phone/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        mobileNumber: '1234567890',
                        password: 'test123'
                    })
                });

                const data = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        document.getElementById('testBtn').addEventListener('click', testAPI);
        
        // Initialize MSW when page loads
        initMSW();
    </script>
</body>
</html>
