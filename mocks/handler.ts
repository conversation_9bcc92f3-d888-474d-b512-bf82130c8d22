// src/mocks/handlers.ts
import { http, HttpResponse } from "msw";

export const handlers = [
  // Test endpoint to verify MSW is working
  http.get("/api/test-msw", () => {
    console.log('MSW: Intercepted GET /api/test-msw');
    return HttpResponse.json({
      message: "MSW is working! This response is mocked.",
      timestamp: new Date().toISOString()
    });
  }),

  http.post("/api/test-msw", async ({ request }) => {
    console.log('MSW: Intercepted POST /api/test-msw');
    const body = await request.json() as any;
    return HttpResponse.json({
      message: "MSW is working! This POST response is mocked.",
      received: body,
      timestamp: new Date().toISOString()
    });
  }),

  // Phone signin endpoint
  http.post("/v1/phone/signin", async ({ request }) => {
    try {
      const body = await request.json() as any;
      console.log('MSW: Intercepted /v1/phone/signin request:', body);

      // Simulate different responses based on request data
      if (body.signinType === 'password') {
        // Password login
        return HttpResponse.json({
          success: true,
          accessToken: "mock_access_token_12345",
          refreshToken: "mock_refresh_token_67890",
          expiresIn: 3600,
          user: {
            id: "user_123",
            mobileNumber: body.mobileNumber,
            countryCode: body.countryCode
          }
        }, { status: 200 });
      } else {
        // Code verification or other signin types
        return HttpResponse.json({
          success: true,
          message: "Verification code sent successfully",
          clientId: "100033",
          captcha: {
            captchaID: "",
            captchaOutput: "",
            genTime: "",
            lotNumber: "",
            passToken: "",
          },
          category: "Text",
          codeLength: 4,
          language: "zh-CN",
          countryCode: body.countryCode || 93,
          mobileNumber: body.mobileNumber || "1234001",
        }, { status: 200 });
      }
    } catch (error) {
      console.error('MSW: Error processing /v1/phone/signin:', error);
      return HttpResponse.json({
        success: false,
        error: "Invalid request format"
      }, { status: 400 });
    }
  }),

  // Phone code send endpoint
  http.post("/v1/phone/code/send", async ({ request }) => {
    try {
      const body = await request.json() as any;
      console.log('MSW: Intercepted /v1/phone/code/send request:', body);

      return HttpResponse.json({
        success: true,
        message: "Verification code sent successfully",
        clientId: body.clientId || "100033",
        codeLength: 4,
        language: body.language || "zh-CN",
        countryCode: body.countryCode || 93,
        mobileNumber: body.mobileNumber,
        category: body.category || "Text"
      }, { status: 200 });
    } catch (error) {
      console.error('MSW: Error processing /v1/phone/code/send:', error);
      return HttpResponse.json({
        success: false,
        error: "Invalid request format"
      }, { status: 400 });
    }
  }),

  // Email signin endpoint
  http.post("/v1/email/signin", async ({ request }) => {
    try {
      const body = await request.json() as any;
      console.log('MSW: Intercepted /v1/email/signin request:', body);

      return HttpResponse.json({
        success: true,
        accessToken: "mock_email_access_token_12345",
        refreshToken: "mock_email_refresh_token_67890",
        expiresIn: 3600,
        user: {
          id: "user_email_123",
          email: body.email
        }
      }, { status: 200 });
    } catch (error) {
      console.error('MSW: Error processing /v1/email/signin:', error);
      return HttpResponse.json({
        success: false,
        error: "Invalid request format"
      }, { status: 400 });
    }
  }),

  // Email code send endpoint
  http.post("/v1/email/code/send", async ({ request }) => {
    try {
      const body = await request.json() as any;
      console.log('MSW: Intercepted /v1/email/code/send request:', body);

      return HttpResponse.json({
        success: true,
        message: "Email verification code sent successfully",
        clientId: body.clientId || "100033",
        codeLength: 4,
        language: body.language || "zh-CN",
        email: body.email,
        category: body.category || "Text"
      }, { status: 200 });
    } catch (error) {
      console.error('MSW: Error processing /v1/email/code/send:', error);
      return HttpResponse.json({
        success: false,
        error: "Invalid request format"
      }, { status: 400 });
    }
  }),
];
