export async function initMocks() {
  if (typeof window === 'undefined') {
    // Node 环境（例如测试或 SSR）
    const { server } = await import('./server');
    server.listen();
    console.log('MSW server started for Node.js environment');
  } else {
    // 浏览器环境
    const { worker } = await import('./browser');
    await worker.start({
      onUnhandledRequest: 'bypass', // 未处理的请求直接绕过
      serviceWorker: {
        url: '/mockServiceWorker.js'
      }
    });
    console.log('MSW worker started for browser environment');
  }
}